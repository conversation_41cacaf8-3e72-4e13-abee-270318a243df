package com.illumio.data.configuration;

import com.illumio.data.util.FlowDataValidator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.metrics.LongCounter;
import io.opentelemetry.api.metrics.Meter;

@Configuration
public class OpenTelemetryMetricsConfig {

    private LongCounter createCounter(OpenTelemetry openTelemetry, String name, String description) {
        Meter meter = openTelemetry.getMeter("ip-classification-mmdb");
        return meter.counterBuilder(name)
                .setDescription(description)
                .build();
    }

    @Bean
    public LongCounter mismatchFieldsCounter(OpenTelemetry openTelemetry) {
        return createCounter(openTelemetry, "mismatch_fields", "These fields were set upstream, but there was a mismatch between the existing value and MMDB value");
    }

    @Bean
    public LongCounter insightIPClassificationEventCounter(OpenTelemetry openTelemetry) {
        return createCounter(openTelemetry, FlowDataValidator.INSIGHTS_INCOMING_EVENTS, "Used to count the number of insight IP classification events");
    }

    @Bean
    public LongCounter insightIPClassificationWrongEvent(OpenTelemetry openTelemetry) {
        return createCounter(openTelemetry, FlowDataValidator.INSIGHTS_INCOMING_WRONG_EVENTS, "Used to count the number of malformed events that reached insight IP classification");
    }
}
