package com.illumio.data.components;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.benmanes.caffeine.cache.AsyncCache;
import com.illumio.data.configuration.MetricsConfiguration;
import com.illumio.data.util.MetricsUtil;
import io.opentelemetry.api.metrics.LongCounter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.ReactiveRedisOperations;
import org.springframework.data.redis.core.ReactiveValueOperations;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.function.BiFunction;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = {MetricsConfiguration.class, MetricsUtil.class})
class LabelLookupTest {

    @Mock
    private AsyncCache<String, String> localCache;

    @Mock
    private ReactiveRedisOperations<String, String> reactiveRedisOperations;

    @Mock
    private ReactiveValueOperations<String, String> reactiveValueOperations;

    @Mock LongCounter labelRecommendationEvent;
    @Mock LongCounter labelRecommendationWrongEvent;


    @Autowired
    private MetricsUtil metricsUtil;

    private LabelLookup labelLookup;

    @BeforeEach
    void setup() {
        this.labelLookup = new LabelLookup(new ObjectMapper(), localCache, reactiveRedisOperations,
            metricsUtil, labelRecommendationEvent, labelRecommendationWrongEvent);
    }

    /**
     * VM/non-VM
     * Expects the json to be returned without decoration
     * when json type is not an object
     */
    @Test
    void enrichWithLabel_invalidJsonObject() {
        JsonNode jsonNode = JsonNodeFactory.instance.arrayNode();
        StepVerifier.create(labelLookup.enrichWithLabel(jsonNode))
                .expectNext(jsonNode)
                .verifyComplete();
    }

    /**
     * non-VM
     * Expects the json to be returned without decoration
     * when SrcCSLabels/DestCSLabels type is not an object.
     */
    @Test
    void enrichWithLabel_nonVm_invalidIncomingLabelsJsonObject() {
        ObjectNode jsonNode = JsonNodeFactory.instance.objectNode();
        jsonNode.put("SrcCSLabels", "non-json");
        jsonNode.put("SrcCSLabels", "non-json");
        StepVerifier.create(labelLookup.enrichWithLabel(jsonNode))
                .expectNext(jsonNode)
                .verifyComplete();
    }

    /**
     * non-VM
     * Expects the json to be returned without decoration
     * when SrcCSLabels and DestCSLabels are missing.
     */
    @Test
    void enrichWithLabel_nonVm_missingIncomingLabels() {
        ObjectNode jsonNode = JsonNodeFactory.instance.objectNode();
        jsonNode.put("SrcResourceType", "Microsoft.Sql/servers");
        jsonNode.put("DestResourceType", "Microsoft.Network/networkInterfaces");
        StepVerifier.create(labelLookup.enrichWithLabel(jsonNode))
                .expectNext(jsonNode)
                .verifyComplete();
    }

    /**
     * non-VM
     * Expects the json to be returned without decoration
     * when SrcCSLabels/DestCSLabels have missing role and service role.
     */
    @Test
    void enrichWithLabel_nonVm_incomingLabels_missingRoles() {
        ObjectNode jsonNode = JsonNodeFactory.instance.objectNode();
        jsonNode.put("SrcResourceType", "Microsoft.Sql/servers");
        jsonNode.put("DestResourceType", "Microsoft.Network/networkInterfaces");
        jsonNode.put("SrcCSLabels", "{\"Name\":\"BozoSrc\"}");
        jsonNode.put("DestCSLabels", "{\"Name\":\"BozoDest\"}");
        StepVerifier.create(labelLookup.enrichWithLabel(jsonNode))
                .expectNext(jsonNode)
                .verifyComplete();
    }

    /**
     * non-VM
     * Expects the json to be returned with decoration
     * when SrcCSLabels/DestCSLabels have user-defined role.
     */
    @Test
    void enrichWithLabel_nonVm_userDefinedLabel() {
        ObjectNode jsonNode = JsonNodeFactory.instance.objectNode();
        jsonNode.put("SrcResourceType", "Microsoft.Sql/servers");
        jsonNode.put("DestResourceType", "Microsoft.Network/networkInterfaces");
        jsonNode.put("SrcCSLabels", "{\"role\":\"SrcApp\", \"servicerole\":\"doesnt_matter\"}");
        jsonNode.put("DestCSLabels", "{\"role\":\"DestApp\", \"servicerole\":\"doesnt_matter\"}");
        StepVerifier.create(labelLookup.enrichWithLabel(jsonNode))
                .expectNextMatches(node ->
                        "SrcApp".equals(node.get("SourceLabel").asText())
                                && "DestApp".equals(node.get("DestinationLabel").asText())

                )
                .verifyComplete();
    }

    /**
     * non-VM
     * Expects the json to be returned with decoration
     * when SrcCSLabels/DestCSLabels doesn't have user-defined role but service role.
     */
    @Test
    void enrichWithLabel_nonVm_serviceLabel() {
        ObjectNode jsonNode = JsonNodeFactory.instance.objectNode();
        jsonNode.put("SrcResourceType", "Microsoft.Sql/servers");
        jsonNode.put("DestResourceType", "Microsoft.Network/networkInterfaces");
        jsonNode.put("SrcCSLabels", "{\"servicerole\":\"Database\"}");
        jsonNode.put("DestCSLabels", "{\"servicerole\":\"Network\"}");
        StepVerifier.create(labelLookup.enrichWithLabel(jsonNode))
                .expectNextMatches(node ->
                        "Database".equals(node.get("SourceLabel").asText())
                                && "Network".equals(node.get("DestinationLabel").asText())

                )
                .verifyComplete();
    }

    /**
     * VM
     * Expects the json to be returned without decoration
     * when SrcCSLabels/DestCSLabels type is not an object, and
     * CSSrcId/DestSrcId is missing
     */
    @Test
    void enrichWithLabel_vm_invalidIncomingLabelsJsonObject() {
        ObjectNode jsonNode = JsonNodeFactory.instance.objectNode();
        jsonNode.put("SrcResourceType", "Microsoft.Compute/virtualMachines");
        jsonNode.put("DestResourceType", "AWS::EC2::Instance");
        jsonNode.put("SrcCSLabels", "non-json");
        jsonNode.put("SrcCSLabels", "non-json");
        StepVerifier.create(labelLookup.enrichWithLabel(jsonNode))
                .expectNext(jsonNode)
                .verifyComplete();
    }

    /**
     * VM
     * Expects the json to be returned with decoration
     * when SrcCSLabels/DestCSLabels have user-defined role.
     */
    @Test
    void enrichWithLabel_vm_userDefinedLabel() {
        ObjectNode jsonNode = JsonNodeFactory.instance.objectNode();
        jsonNode.put("SrcResourceType", "Microsoft.Compute/virtualMachines");
        jsonNode.put("DestResourceType", "AWS::EC2::Instance");
        jsonNode.put("SrcCSLabels", "{\"role\":\"SrcApp\", \"servicerole\":\"doesnt_matter\"}");
        jsonNode.put("DestCSLabels", "{\"role\":\"DestApp\", \"servicerole\":\"doesnt_matter\"}");
        StepVerifier.create(labelLookup.enrichWithLabel(jsonNode))
                .expectNextMatches(node ->
                        "SrcApp".equals(node.get("SourceLabel").asText())
                                && "DestApp".equals(node.get("DestinationLabel").asText())

                )
                .verifyComplete();
    }

    /**
     * VM
     * Expects the json to be returned without decoration
     * when user-defined role, and CSSrcId and CSDestId fields are missing.
     */
    @Test
    void enrichWithLabel_vm_missingIds() {
        ObjectNode jsonNode = JsonNodeFactory.instance.objectNode();
        jsonNode.put("SrcResourceType", "Microsoft.Compute/virtualMachines");
        jsonNode.put("DestResourceType", "AWS::EC2::Instance");
        StepVerifier.create(labelLookup.enrichWithLabel(jsonNode))
                .expectNext(jsonNode)
                .verifyComplete();
    }

    /**
     * VM: no user-defined role
     * Expects the json to be returned without decoration
     * when labels are missing for resource CSSrcId and CSDestId.
     */
    @Test
    void enrichWithLabel_vm_localCacheHit_emptyValue() {
        ObjectNode jsonNode = JsonNodeFactory.instance.objectNode();
        jsonNode.put("SrcResourceType", "Microsoft.Compute/virtualMachines");
        jsonNode.put("DestResourceType", "AWS::EC2::Instance");
        jsonNode.put("CSSrcId", "resource-x");
        jsonNode.put("CSDestId", "resource-y");

        when(localCache.get(eq("resource-x"), ArgumentMatchers.<BiFunction<String, Executor, CompletableFuture<String>>>any()))
                .thenReturn(CompletableFuture.completedFuture(""));
        when(localCache.get(eq("resource-y"), ArgumentMatchers.<BiFunction<String, Executor, CompletableFuture<String>>>any()))
                .thenReturn(CompletableFuture.completedFuture(""));

        StepVerifier.create(labelLookup.enrichWithLabel(jsonNode))
                .expectNext(jsonNode)
                .verifyComplete();
    }

    /**
     * VM: no user-defined role
     * Expects the json to be returned with decoration from the local cache.
     */
    @Test
    void enrichWithLabel_vm_localCacheHit() {
        ObjectNode jsonNode = JsonNodeFactory.instance.objectNode();
        jsonNode.put("SrcResourceType", "Microsoft.Compute/virtualMachines");
        jsonNode.put("DestResourceType", "AWS::EC2::Instance");
        jsonNode.put("CSSrcId", "resource-x");
        jsonNode.put("CSDestId", "resource-y");

        when(localCache.get(eq("resource-x"), ArgumentMatchers.<BiFunction<String, Executor, CompletableFuture<String>>>any()))
                .thenReturn(CompletableFuture.completedFuture("label-x"));
        when(localCache.get(eq("resource-y"), ArgumentMatchers.<BiFunction<String, Executor, CompletableFuture<String>>>any()))
                .thenReturn(CompletableFuture.completedFuture("label-y"));

        StepVerifier.create(labelLookup.enrichWithLabel(jsonNode))
                .expectNextMatches(node ->
                        "label-x".equals(node.get("SourceLabel").asText())
                                && "label-y".equals(node.get("DestinationLabel").asText())

                )
                .verifyComplete();
    }

    /**
     * VM: no user-defined role
     * Expects the json to be returned with decoration from the external Redis cache.
     */
    @Test
    void enrichWithLabel_vm_redisCacheHit() {
        ObjectNode jsonNode = JsonNodeFactory.instance.objectNode();
        jsonNode.put("SrcResourceType", "Microsoft.Compute/virtualMachines");
        jsonNode.put("DestResourceType", "AWS::EC2::Instance");
        jsonNode.put("CSSrcId", "resource-x");
        jsonNode.put("CSDestId", "resource-y");

        when(localCache.get(any(String.class), ArgumentMatchers.<BiFunction<String, Executor, CompletableFuture<String>>>any()))
                .thenAnswer(invocationOnMock -> {
                    String key = invocationOnMock.getArgument(0);
                    BiFunction<String, Executor, CompletableFuture<String>> fn = invocationOnMock.getArgument(1);
                    return fn.apply(key, Executors.newSingleThreadExecutor());
                });
        when(reactiveRedisOperations.opsForValue()).thenReturn(reactiveValueOperations);
        when(reactiveValueOperations.get("resource-x")).thenReturn(Mono.just("label-x"));
        when(reactiveValueOperations.get("resource-y")).thenReturn(Mono.just("label-y"));

        StepVerifier.create(labelLookup.enrichWithLabel(jsonNode))
                .expectNextMatches(node ->
                        "label-x".equals(node.get("SourceLabel").asText())
                                && "label-y".equals(node.get("DestinationLabel").asText())

                )
                .verifyComplete();
    }

    /**
     * VM: no user-defined role
     * Expects the json to be returned without decoration
     * when labels are missing for resource CSSrcId and CSDestId in Redis cache.
     */
    @Test
    void enrichWithLabel_vm_redisCacheHitEmpty() {
        ObjectNode jsonNode = JsonNodeFactory.instance.objectNode();
        jsonNode.put("SrcResourceType", "Microsoft.Compute/virtualMachines");
        jsonNode.put("DestResourceType", "AWS::EC2::Instance");
        jsonNode.put("CSSrcId", "resource-x");
        jsonNode.put("CSDestId", "resource-y");

        when(localCache.get(any(String.class), ArgumentMatchers.<BiFunction<String, Executor, CompletableFuture<String>>>any()))
                .thenAnswer(invocationOnMock -> {
                    String key = invocationOnMock.getArgument(0);
                    BiFunction<String, Executor, CompletableFuture<String>> fn = invocationOnMock.getArgument(1);
                    return fn.apply(key, Executors.newSingleThreadExecutor());
                });

        when(reactiveRedisOperations.opsForValue()).thenReturn(reactiveValueOperations);
        when(reactiveValueOperations.get("resource-x")).thenReturn(Mono.empty());
        when(reactiveValueOperations.get("resource-y")).thenReturn(Mono.empty());

        StepVerifier.create(labelLookup.enrichWithLabel(jsonNode))
                .expectNext(jsonNode)
                .verifyComplete();
    }

    /**
     * Source VM/ Dest non-VM
     * Expects the json to be returned with decoration
     */
    @Test
    void enrichWithLabel_incomingLabels() {
        ObjectNode jsonNode = JsonNodeFactory.instance.objectNode();
        jsonNode.put("SrcResourceType", "Microsoft.Compute/virtualMachines");
        jsonNode.put("DestResourceType", "anyNonVM");
        jsonNode.put("SrcCSLabels", "{\"role\":\"SrcApp\", \"servicerole\":\"doesnt_matter\"}");
        jsonNode.put("DestCSLabels", "{\"servicerole\":\"DestService\"}");
        StepVerifier.create(labelLookup.enrichWithLabel(jsonNode))
                .expectNextMatches(node ->
                        "SrcApp".equals(node.get("SourceLabel").asText())
                                && "DestService".equals(node.get("DestinationLabel").asText())

                )
                .verifyComplete();
    }
}